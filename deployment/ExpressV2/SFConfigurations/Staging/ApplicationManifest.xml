<?xml version="1.0" encoding="utf-8"?>
<ApplicationManifest xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" ApplicationTypeName="Providers.ServiceFabricType" ApplicationTypeVersion="1.0.0" xmlns="http://schemas.microsoft.com/2011/01/fabric">
  <Parameters>
    <Parameter Name="RoleLocation" DefaultValue="" />
    <Parameter Name="Region" DefaultValue="" />
    <Parameter Name="TenantId" DefaultValue="72f988bf-86f1-41af-91ab-2d7cd011db47" />
    <Parameter Name="MainTemplateFilePath_Dev" DefaultValue="" />
    <Parameter Name="MainTemplateFilePath_Staging" DefaultValue="" />
    <Parameter Name="MainTemplateFilePath_Prod" DefaultValue="" />
    <Parameter Name="KeyVaultEndpoint" DefaultValue="" />
    <Parameter Name="DatabricksNetworkIntentPoliciesFilePath_Dev" DefaultValue="" />
    <Parameter Name="DatabricksNetworkIntentPoliciesFilePath_Staging" DefaultValue="" />
    <Parameter Name="DatabricksNetworkIntentPoliciesFilePath_Prod" DefaultValue="" />
    <Parameter Name="DatabricksOutboundNetworkEndpointsFilePath" DefaultValue="" />
    <Parameter Name="DatabricksOutboundNetworkEndpointsFilePath_NPIP" DefaultValue="" />
    <Parameter Name="AADCertThumbprint" DefaultValue="" />
    <Parameter Name="SSLCertSubjectName" DefaultValue="" />
    <Parameter Name="AADCertSubjectName" DefaultValue="" />
    <Parameter Name="ApplicationPort" DefaultValue="" />
    <Parameter Name="JobDefinitionsTableName" DefaultValue="" />
    <Parameter Name="JobTriggersQueuePrefix" DefaultValue="" />
    <Parameter Name="KustoClusterUri" DefaultValue="" />
    <Parameter Name="PrimaryJobsDataStorageAccountName" DefaultValue="" />
    <Parameter Name="SecondaryJobsDataStorageAccountName" DefaultValue="" />
    <Parameter Name="WorkspaceDataStorageAccountName" DefaultValue="" />
    <Parameter Name="DSCPackageStorageAccountNameSuffix" DefaultValue="" />
    <Parameter Name="ARGEndpoint" DefaultValue="" />
    <Parameter Name="ShortRegionName" DefaultValue="" />
  </Parameters>
  <!-- Import the ServiceManifest from the ServicePackage. The ServiceManifestName and ServiceManifestVersion 
       should match the Name and Version attributes of the ServiceManifest element defined in the 
       ServiceManifest.xml file. -->
  <ServiceManifestImport>
    <ServiceManifestRef ServiceManifestName="Providers.ServiceFabric.FeatureRolePkg" ServiceManifestVersion="1.0.0" />
    <ConfigOverrides>
      <ConfigOverride Name="Config">
        <Settings>
          <Section Name="FeatureRoleConfigSection">
            <Parameter Name="CloudEnvironment" Value="public" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AADAuthenticationEnabled" Value="true" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.RoleLocation" Value="[RoleLocation]" />
            <Parameter Name="MainTemplateFilePath_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="[MainTemplateFilePath_Dev]" />
            <Parameter Name="MainTemplateFilePath_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="[MainTemplateFilePath_Staging]" />
            <Parameter Name="MainTemplateFilePath_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="[MainTemplateFilePath_Prod]" />
            <Parameter Name="KeyVault_Endpoint" Value="[KeyVaultEndpoint]" />
            <Parameter Name="MdmAccount" Value="AzureDatabricksRPStgWarm" />
            <Parameter Name="JarvisEndpoint" Value="Test" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FrontdoorEndpointUri" Value="https://management.azure.com/" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ArmMetadataEndpoint" Value="https://admin.management.azure.com/metadata/authentication?api-version=2015-01-01" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.DatabricksApplicationId" Value="02f14ed1-a518-47a4-ac2b-9220f17eae29" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.ServicePrincipalCertificateThumbprint" Value="[AADCertThumbprint]" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.ServicePrincipalCertificateSubjectName" Value="[AADCertSubjectName]" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.SSLCertificateSubjectName" Value="[SSLCertSubjectName]" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.AadAllowedAudience" Value="https://management.azure.com/" />
            <Parameter Name="AzureAD_SecretsAppId" Value="02f14ed1-a518-47a4-ac2b-9220f17eae29" />
            <Parameter Name="AzureAD_SecretsTenantId" Value="[TenantId]" />
            <Parameter Name="AzureAD_SecretsLoginTemplate" Value="https://login.microsoftonline.com/" />
            <Parameter Name="PublisherTenantId_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="9f37a392-f0ae-4280-9796-f1864a10effc" />
            <Parameter Name="AuthPrincipalId_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="c464d341-789c-464c-9b1b-6c45bd3d9a4c" />
            <Parameter Name="AuthPrincipalType_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="Group" />
            <Parameter Name="PublisherLoginAppdId_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="62a912ac-b58e-4c1d-89ea-b2dbfc7358fc" />
            <Parameter Name="PublisherTenantId_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="72f988bf-86f1-41af-91ab-2d7cd011db47" />
            <Parameter Name="AuthPrincipalId_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="c9b79afc-8394-4a51-83ca-eb5de6046e89" />
            <Parameter Name="AuthPrincipalType_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="Group" />
            <Parameter Name="PublisherLoginAppdId_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="4a67d088-db5c-48f1-9ff2-0aace800ae68" />
            <Parameter Name="PublisherTenantId_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="72f988bf-86f1-41af-91ab-2d7cd011db47" />
            <Parameter Name="AuthPrincipalId_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="9a74af6f-d153-4348-988a-e2672920bee9" />
            <Parameter Name="AuthPrincipalType_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="Group" />
            <Parameter Name="PublisherLoginAppdId_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="2ff814a6-3304-4ab8-85cb-cd0e6f879c1d" />
            <Parameter Name="DatabricksProdEnvironmentLoginAppId" Value="2ff814a6-3304-4ab8-85cb-cd0e6f879c1d"/>
            <Parameter Name="DatabricksStagingEnvironmentLoginAppId" Value="4a67d088-db5c-48f1-9ff2-0aace800ae68"/>
            <Parameter Name="DatabricksDevEnvironmentLoginAppId" Value="62a912ac-b58e-4c1d-89ea-b2dbfc7358fc"/>
            <Parameter Name="DatabricksAccountsMgrDns" Value="https://accounts.azuredatabricks.net/"/>
            <Parameter Name="DatabricksAccountsMgrDevStagingDnsTemplate" Value="https://accounts.{0}.azuredatabricks.net/"/>
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.VulnScanRule.LocalPorts" Value="" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.VulnScanRule.ConfigureRules" Value="" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.VulnScanRule.RemoteIPs" Value="" />
            <Parameter Name="MonitoringAgentVersion" Value="1.0" />
            <Parameter Name="MonitoringInitConfig" Value="MdsConfig_Providers.xml" />
            <Parameter Name="Monitoring_GCS_Environment" Value="Test" />
            <Parameter Name="Monitoring_GCS_Account" Value="AzureDatabricksRPStgWarm" />
            <Parameter Name="Monitoring_GCS_Namespace" Value="AzureDatabricksRPStgWarm" />
            <Parameter Name="Monitoring_GCS_Thumbprint" Value="[AADCertThumbprint]" />
            <Parameter Name="Monitoring_Config_Version" Value="21.2" />
            <Parameter Name="Monitoring_Agent_Version" Value="44.4.1" />
            <Parameter Name="Monitoring_Use_Geneva_Config_Service" Value="true" />
            <Parameter Name="SLIMetricsMonitoringAccount" Value="AzureDatabricksRPStgHot" />
            <Parameter Name="SLIMetricsNamespace" Value="SLIMetrics" />
            <Parameter Name="EnableSLIMetricsCapture" Value="true" />
            <Parameter Name="AllowedSLISyncOperations" Value="GET/SUBSCRIPTIONS/RESOURCEGROUPS/PROVIDERS/MICROSOFT.DATABRICKS/WORKSPACES/;PATCH/SUBSCRIPTIONS/RESOURCEGROUPS/PROVIDERS/MICROSOFT.DATABRICKS/WORKSPACES/;PUT/SUBSCRIPTIONS/RESOURCEGROUPS/PROVIDERS/MICROSOFT.DATABRICKS/WORKSPACES/;DELETE/SUBSCRIPTIONS/RESOURCEGROUPS/PROVIDERS/MICROSOFT.DATABRICKS/WORKSPACES/;GET/SUBSCRIPTIONS/RESOURCEGROUPS/PROVIDERS/MICROSOFT.DATABRICKS/WORKSPACES/OUTBOUNDNETWORKDEPENDENCIESENDPOINTS" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.DisableSN" Value="false" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FailHealthCheck" Value="false" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AllowedReadOnlyClientCertificates" Value="" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AllowedAdminCertificates" Value="adbrpacisclientcert.azclient.ms" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FeatureDataStorage.RequestOptionsServerTimeout" Value="00:00:06" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FeatureDataStorage.RequestOptionsMaximumExecutionTime" Value="00:00:30" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FeatureDataStorage.RequestOptionsDeltaBackoff" Value="00:00:01" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FeatureDataStorage.RequestOptionsMaximumAttempts" Value="3" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDataStorage.RequestOptionsServerTimeout" Value="00:00:06" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDataStorage.RequestOptionsMaximumExecutionTime" Value="00:00:30" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDataStorage.RequestOptionsDeltaBackoff" Value="00:00:01" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDataStorage.RequestOptionsMaximumAttempts" Value="3" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AppliancePackageDataStorage.RequestOptionsServerTimeout" Value="00:00:06" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AppliancePackageDataStorage.RequestOptionsMaximumExecutionTime" Value="00:00:30" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AppliancePackageDataStorage.RequestOptionsDeltaBackoff" Value="00:00:01" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AppliancePackageDataStorage.RequestOptionsMaximumAttempts" Value="3" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.DefaultRetryInterval" Value="00:00:15" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.AadLoginTemplate" Value="https://login.microsoftonline.com/" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Databricks.Workspace.PublisherPackageIdMappings" Value="2016-09-01-preview#databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12;2017-08-01-preview#databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12;2017-09-01-preview#databricks.databricks-workspace-previewdatabricks-workspace.0.0.2;2018-03-01#databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12;2018-03-15#databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12;2018-04-01#databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.RetryInterval" Value="01:00:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.StartTime.Immediate" Value="00:00:30" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.EndTime" Value="07:00:00:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.Timeout" Value="00:10:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.RetryInterval" Value="01:00:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.StartTime.Immediate" Value="00:00:30" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.EndTime" Value="01:00:00:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.Timeout" Value="00:10:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Feature.ConfigurationCache.CacheInitializationTimeout" Value="00:01:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServiceClient.RequestTimeout" Value="00:01:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServiceClient.ConnectionLeaseTimeout" Value="00:01:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServiceClient.MaxResponseContentSize" Value="8388608" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.BackgroundJobs.JobDefinitionsTableName" Value="[JobDefinitionsTableName]" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.BackgroundJobs.JobTriggersQueuePrefix" Value="[JobTriggersQueuePrefix]" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Feature.TelemetryRequest.TotalEventsLimit" Value="100" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Feature.TelemetryRequest.AllowedApplications" Value="c44b4083-3bb0-49c1-b47d-974e53cbdf3c;d1208c34-6081-4127-96fa-8c97ecb420c1;475226c6-020e-4fb2-8a90-7a972cbfc1d4;4e291c71-d680-4d0e-9640-0a3358e31177;67d4a015-f730-42e3-b3c9-058df3332f45;5e26e33b-bc1f-4838-b3a6-48a3492df6e8;6204c1d1-4712-4c46-a7d9-3ed63d992682;872cd9fa-d31f-45e0-9eab-6e460a02d1f1;b677c290-cf4b-4a8e-a60e-91ba650a4abe;7f59a773-2eaf-429c-a059-50fc5bb28b44;0c1307d4-29d6-4389-a11c-5cbe7f65d7fa;c836cbdb-7a5b-44cc-a54f-564b4b486fc6;c44b4083-3bb0-49c1-b47d-974e53cbdf3c" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServicePointDefaultConnectionLimitPerProcessor" Value="128" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServicePointMaxServicePointIdleTime" Value="50000" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServicePointUseNagleAlgorithm" Value="false" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServicePointExpect100Continue" Value="false" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ThreadPoolMinWorkerThreads" Value="24" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ThreadPoolMaxWorkerThreads" Value="" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ThreadPoolMinIoThreads" Value="24" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ThreadPoolMaxIoThreads" Value="" />
            <Parameter Name="DatabricksNetworkIntentPoliciesFilePath_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="[DatabricksNetworkIntentPoliciesFilePath_Dev]" />
            <Parameter Name="DatabricksNetworkIntentPoliciesFilePath_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="[DatabricksNetworkIntentPoliciesFilePath_Staging]" />
            <Parameter Name="DatabricksNetworkIntentPoliciesFilePath_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="[DatabricksNetworkIntentPoliciesFilePath_Prod]" />
            <Parameter Name="GetNetworkIntentPoliciesAllowList" Value="2cf9eb86-36b5-49dc-86ae-9a63135dfa8c" />
            <Parameter Name="VirtualNetworkInjectionOperationsApiVersion" Value="2019-06-01" />
            <Parameter Name="BlobEndpointSuffix" Value="blob.core.windows.net" />
            <Parameter Name="DfsEndpointSuffix" Value="dfs.core.windows.net" />
            <Parameter Name="DatabricksOutboundNetworkEndpointsFilePath" Value="[DatabricksOutboundNetworkEndpointsFilePath]" />
            <Parameter Name="DatabricksOutboundNetworkEndpointsFilePath_NPIP" Value="[DatabricksOutboundNetworkEndpointsFilePath_NPIP]" />
            <Parameter Name="DenyAssignmentsApiVersion" Value="2019-03-01-preview" />
            <Parameter Name="DisableValidateNotActions" Value="false" />
            <Parameter Name="FeaturesApiVersion" Value="2015-12-01" />
            <Parameter Name="StorageApiVersion" Value="2022-09-01" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Identity.RequestRetryCount" Value="3" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Identity.RequestRetryInterval" Value="00:00:01" />
            <Parameter Name="ManagedIdentityDataPlaneApiVersion" Value="2023-09-30" />
            <Parameter Name="ManagedIdentityDataPlaneAudience" Value="https://serviceidentity.azure.net/" />
            <Parameter Name="ManagedIdentityControlPlaneApiVersion" Value="2018-11-30" />
            <Parameter Name="RelayApiVersion" Value="2017-04-01" />
            <Parameter Name="DisableFedRampCertificationCheck" Value="false" />
            <Parameter Name="PrivateLinkMinApiVersion" Value="2018-03-01" />
            <Parameter Name="DatabricksUIAPIRequiredDNSZone" Value="privatelink.azuredatabricks.net" />
            <Parameter Name="DatabricksPrivateLinkTemplateUri" Value="privatelink.{0}.azuredatabricks.net" />
            <Parameter Name="EnableDatabricksNotification" Value="true" />
            <Parameter Name="DisablePublicNetworkAccessCheck" Value="false" />
            <Parameter Name="EnableDBWorkspaceNotification" Value="true" />
            <Parameter Name="DBWorkspaceNotificationAllowedApiVersions" Value="" />
            <Parameter Name="DBWorkspaceNotificationRetryIntervalSecs" Value="5" />
            <Parameter Name="DBWorkspaceNotificationRetryCount" Value="3" />
            <Parameter Name="AmlWorkspaceApiVersion" Value="2018-11-19" />
            <Parameter Name="DisableRoleAssignmentsPreviewCheck"  Value="false" />
            <Parameter Name="RBACMinApiVersion" Value="2021-10-01-preview" />
            <Parameter Name="DefaultWorkspaceUri" Value="" />
            <Parameter Name="EnableWorkspaceRBACNotification" Value="true" />
            <Parameter Name="EnableDatabricksServicePrincipalTypeEndpoint" Value="true" />
            <Parameter Name="EnableGraphBatchRequest" Value="true"/>
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Frontdoor.Graph.Endpoint" Value="https://graph.microsoft.com/" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.ApplianceDataProvider.RetryInterval" Value="00:00:01" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.ApplianceDataProvider.RetryTimeout" Value="00:02:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.ApplianceDataProvider.MaxRetryAttempts" Value="3" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Region" Value="[Region]" />
            <Parameter Name="VNetInjectionMandatoryApiVersions" Value="2025-02-01-preview;2025-06-01-preview" />  
            <Parameter Name="AllowedStorageAccountSKUNames" Value="Standard_LRS;Standard_GRS;Standard_RAGRS;Standard_ZRS;Premium_LRS;Premium_ZRS;Standard_GZRS;Standard_RAGZRS" />
            <Parameter Name="EnableSubscriptionDeletionJob" Value="true" />
            <Parameter Name="PrimaryJobsDataStorageAccountName" Value="[PrimaryJobsDataStorageAccountName]" />
            <Parameter Name="SecondaryJobsDataStorageAccountName" Value="[SecondaryJobsDataStorageAccountName]" />
            <Parameter Name="WorkspaceDataStorageAccountName" Value="[WorkspaceDataStorageAccountName]" />
            <Parameter Name="DSCPackageStorageAccountNameSuffix" Value="[DSCPackageStorageAccountNameSuffix]" />
            <Parameter Name="ARGEndpoint" Value="[ARGEndpoint]" />
            <Parameter Name="ShortRegionName" Value="[ShortRegionName]" />
            <Parameter Name="AllowedDevLocationsForNoAzureServiceRules" Value="japaneast;koreacentral" />
            <Parameter Name="AllowedStagingLocationsForNoAzureServiceRules" Value="NA" />
            <Parameter Name="AllowedProdLocationsForNoAzureServiceRules" Value="eastus2euap;centralus;eastus;australiaeast;eastus2;northeurope;westus2;westeurope" />
          </Section>
        </Settings>
      </ConfigOverride>
    </ConfigOverrides>
    <ResourceOverrides>
      <Endpoints>
        <Endpoint Name="ProvidersFeatureServiceEndpoint" Type="Input" Protocol="https" Port="[ApplicationPort]" />
      </Endpoints>
    </ResourceOverrides>
    <Policies>
      <EndpointBindingPolicy EndpointRef="ProvidersFeatureServiceEndpoint" CertificateRef="MyCert" />
    </Policies>
  </ServiceManifestImport>
  <ServiceManifestImport>
    <ServiceManifestRef ServiceManifestName="Providers.ServiceFabric.WorkerRolePkg" ServiceManifestVersion="1.0.0" />
    <ConfigOverrides>
      <ConfigOverride Name="Config">
        <Settings>
          <Section Name="WorkerRoleConfigSection">
            <Parameter Name="CloudEnvironment" Value="public" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.RoleLocation" Value="[RoleLocation]" />
            <Parameter Name="MainTemplateFilePath_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="[MainTemplateFilePath_Dev]" />
            <Parameter Name="MainTemplateFilePath_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="[MainTemplateFilePath_Staging]" />
            <Parameter Name="MainTemplateFilePath_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="[MainTemplateFilePath_Prod]" />
            <Parameter Name="KeyVault_Endpoint" Value="[KeyVaultEndpoint]" />
            <Parameter Name="MdmAccount" Value="AzureDatabricksRPStgWarm" />
            <Parameter Name="JarvisEndpoint" Value="Test" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FrontdoorEndpointUri" Value="https://management.azure.com/" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ArmMetadataEndpoint" Value="https://admin.management.azure.com/metadata/authentication?api-version=2015-01-01" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.DatabricksApplicationId" Value="02f14ed1-a518-47a4-ac2b-9220f17eae29" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.ServicePrincipalCertificateThumbprint" Value="[AADCertThumbprint]" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.ServicePrincipalCertificateSubjectName" Value="[AADCertSubjectName]" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.AadAllowedAudience" Value="https://management.azure.com/" />
            <Parameter Name="AzureAD_SecretsAppId" Value="02f14ed1-a518-47a4-ac2b-9220f17eae29" />
            <Parameter Name="AzureAD_SecretsTenantId" Value="[TenantId]" />
            <Parameter Name="AzureAD_SecretsLoginTemplate" Value="https://login.microsoftonline.com/" />
            <Parameter Name="PublisherTenantId_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="9f37a392-f0ae-4280-9796-f1864a10effc" />
            <Parameter Name="AuthPrincipalId_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="c464d341-789c-464c-9b1b-6c45bd3d9a4c" />
            <Parameter Name="AuthPrincipalType_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="Group" />
            <Parameter Name="PublisherTenantId_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="72f988bf-86f1-41af-91ab-2d7cd011db47" />
            <Parameter Name="AuthPrincipalId_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="c9b79afc-8394-4a51-83ca-eb5de6046e89" />
            <Parameter Name="AuthPrincipalType_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="Group" />
            <Parameter Name="PublisherTenantId_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="72f988bf-86f1-41af-91ab-2d7cd011db47" />
            <Parameter Name="AuthPrincipalId_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="9a74af6f-d153-4348-988a-e2672920bee9" />
            <Parameter Name="AuthPrincipalType_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="Group" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.VulnScanRule.LocalPorts" Value="" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.VulnScanRule.ConfigureRules" Value="" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.VulnScanRule.RemoteIPs" Value="" />
            <Parameter Name="MonitoringAgentVersion" Value="1.0" />
            <Parameter Name="MonitoringInitConfig" Value="MdsConfig_Providers.xml" />
            <Parameter Name="Monitoring_GCS_Environment" Value="Test" />
            <Parameter Name="Monitoring_GCS_Account" Value="AzureDatabricksRPStgWarm" />
            <Parameter Name="Monitoring_GCS_Namespace" Value="AzureDatabricksRPStgWarm" />
            <Parameter Name="Monitoring_GCS_Thumbprint" Value="[AADCertThumbprint]" />
            <Parameter Name="Monitoring_Config_Version" Value="21.2" />
            <Parameter Name="Monitoring_Agent_Version" Value="44.4.1" />
            <Parameter Name="Monitoring_Use_Geneva_Config_Service" Value="true" />
            <Parameter Name="SLIMetricsMonitoringAccount" Value="AzureDatabricksRPStgHot" />
            <Parameter Name="SLIMetricsNamespace" Value="SLIMetrics" />
            <Parameter Name="EnableSLIMetricsCapture" Value="true" />
            <Parameter Name="AllowedSLISyncOperations" Value="GET/SUBSCRIPTIONS/RESOURCEGROUPS/PROVIDERS/MICROSOFT.DATABRICKS/WORKSPACES/" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.DisableSN" Value="false" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FailHealthCheck" Value="false" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AllowedReadOnlyClientCertificates" Value="" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AllowedAdminCertificates" Value="" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FeatureDataStorage.RequestOptionsServerTimeout" Value="00:00:06" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FeatureDataStorage.RequestOptionsMaximumExecutionTime" Value="00:00:30" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FeatureDataStorage.RequestOptionsDeltaBackoff" Value="00:00:01" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FeatureDataStorage.RequestOptionsMaximumAttempts" Value="3" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDataStorage.RequestOptionsServerTimeout" Value="00:00:06" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDataStorage.RequestOptionsMaximumExecutionTime" Value="00:00:30" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDataStorage.RequestOptionsDeltaBackoff" Value="00:00:01" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDataStorage.RequestOptionsMaximumAttempts" Value="3" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AppliancePackageDataStorage.RequestOptionsServerTimeout" Value="00:00:06" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AppliancePackageDataStorage.RequestOptionsMaximumExecutionTime" Value="00:00:30" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AppliancePackageDataStorage.RequestOptionsDeltaBackoff" Value="00:00:01" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AppliancePackageDataStorage.RequestOptionsMaximumAttempts" Value="3" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.DefaultRetryInterval" Value="00:00:15" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.AadLoginTemplate" Value="https://login.microsoftonline.com/" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Databricks.Workspace.PublisherPackageIdMappings" Value="2016-09-01-preview#databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12;2017-08-01-preview#databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12;2017-09-01-preview#databricks.databricks-workspace-previewdatabricks-workspace.0.0.2;2018-03-01#databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12;2018-03-15#databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12;2018-04-01#databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.RetryInterval" Value="01:00:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.StartTime.Immediate" Value="00:00:30" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.EndTime" Value="07:00:00:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.Timeout" Value="00:10:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.RetryInterval" Value="01:00:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.PostponeInterval" Value="00:00:10" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.MaxRetries" Value="3" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.StartTime.Immediate" Value="00:00:30" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.EndTime" Value="01:00:00:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.Timeout" Value="00:10:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Feature.ConfigurationCache.CacheInitializationTimeout" Value="00:01:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServiceClient.RequestTimeout" Value="00:01:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServiceClient.ConnectionLeaseTimeout" Value="00:01:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServiceClient.MaxResponseContentSize" Value="8388608" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.BackgroundJobs.JobDefinitionsTableName" Value="[JobDefinitionsTableName]" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.BackgroundJobs.JobTriggersQueuePrefix" Value="[JobTriggersQueuePrefix]" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Feature.TelemetryRequest.TotalEventsLimit" Value="100" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Feature.TelemetryRequest.AllowedApplications" Value="c44b4083-3bb0-49c1-b47d-974e53cbdf3c;d1208c34-6081-4127-96fa-8c97ecb420c1;475226c6-020e-4fb2-8a90-7a972cbfc1d4;4e291c71-d680-4d0e-9640-0a3358e31177;67d4a015-f730-42e3-b3c9-058df3332f45;5e26e33b-bc1f-4838-b3a6-48a3492df6e8;6204c1d1-4712-4c46-a7d9-3ed63d992682;872cd9fa-d31f-45e0-9eab-6e460a02d1f1;b677c290-cf4b-4a8e-a60e-91ba650a4abe;7f59a773-2eaf-429c-a059-50fc5bb28b44;0c1307d4-29d6-4389-a11c-5cbe7f65d7fa;c836cbdb-7a5b-44cc-a54f-564b4b486fc6;c44b4083-3bb0-49c1-b47d-974e53cbdf3c" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServicePointDefaultConnectionLimitPerProcessor" Value="128" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServicePointMaxServicePointIdleTime" Value="50000" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServicePointUseNagleAlgorithm" Value="false" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServicePointExpect100Continue" Value="false" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ThreadPoolMinWorkerThreads" Value="24" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ThreadPoolMaxWorkerThreads" Value="" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ThreadPoolMinIoThreads" Value="24" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ThreadPoolMaxIoThreads" Value="" />
            <Parameter Name="DatabricksNetworkIntentPoliciesFilePath_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="[DatabricksNetworkIntentPoliciesFilePath_Dev]" />
            <Parameter Name="DatabricksNetworkIntentPoliciesFilePath_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="[DatabricksNetworkIntentPoliciesFilePath_Staging]" />
            <Parameter Name="DatabricksNetworkIntentPoliciesFilePath_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="[DatabricksNetworkIntentPoliciesFilePath_Prod]" />
            <Parameter Name="GetNetworkIntentPoliciesAllowList" Value="2cf9eb86-36b5-49dc-86ae-9a63135dfa8c" />
            <Parameter Name="VirtualNetworkInjectionOperationsApiVersion" Value="2019-06-01" />
            <Parameter Name="DenyAssignmentsApiVersion"  Value="2019-03-01-preview" />
            <Parameter Name="DenyAssignmentsActions" Value="*" />
            <Parameter Name="DenyAssignmentsNotActions" Value="*/read;Microsoft.Network/virtualNetworks/peer/action;Microsoft.Security/advancedThreatProtectionSettings/*;Microsoft.Storage/storageAccounts/services/diagnosticSettings/*;Microsoft.Authorization/policyExemptions/*;Microsoft.Insights/diagnosticsettings/*;Microsoft.CostManagement/*;Microsoft.Consumption/*;Microsoft.Storage/storageAccounts/privateEndpointConnections/*;Microsoft.Storage/storageAccounts/PrivateEndpointConnectionsApproval/action;Microsoft.Authorization/roleAssignments/write;Microsoft.Security/locations/alerts/dismiss/action;Microsoft.Advisor/*;Microsoft.Security/DefenderForStorageSettings/write;" />
            <Parameter Name="DenyAssignmentsDataActions" Value="*" />
            <Parameter Name="DenyAssignmentsNotDataActions" Value="" />
            <Parameter Name="FeaturesApiVersion"  Value="2015-12-01" />
            <Parameter Name="StorageApiVersion" Value="2022-09-01" />
            <Parameter Name="ServiceEndpointsApiVersion" Value="2021-03-01" />
            <Parameter Name="SupportedServiceEndpointServicesOnManagedVNet" Value="Microsoft.Storage" />
            <Parameter Name="WorkspaceInitializationRetryInterval" Value="30" />
            <Parameter Name="WorkspaceInitializationRetryCount" Value="10" />
            <Parameter Name="DisableFedRampCertificationCheck" Value="false" />
            <Parameter Name="PrivateLinkMinApiVersion" Value="2018-03-01" />
            <Parameter Name="PrivateLinkOperationsApiVersion" Value="2019-12-01" />
            <Parameter Name="PrivateLinkPostponeInterval" Value="00:00:10" />
            <Parameter Name="PrivateLinkMaxLifetime" Value="15" />
            <Parameter Name="EnableDatabricksNotification" Value="true" />
            <Parameter Name="PrivateLinkMaxRetries" Value="3" />            
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.RegisterManagedByTenantApiVersion" Value="2019-03-01" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.UnregisterManagedByTenantEnabled" Value="false" />
            <Parameter Name="EnableDBWorkspaceNotification" Value="true" />
            <Parameter Name="DBWorkspaceNotificationAllowedApiVersions" Value="" />
            <Parameter Name="DBWorkspaceNotificationRetryIntervalSecs" Value="5" />
            <Parameter Name="DBWorkspaceNotificationRetryCount" Value="3" />
            <Parameter Name="AmlWorkspaceApiVersion" Value="2018-11-19" />
            <Parameter Name="DBNotificationPropagationPostponeInterval" Value="00:10:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Region" Value="[Region]" />
            <Parameter Name="ActiveWorkspacesTableName" Value="ActiveWorkspacesTable" />
            <Parameter Name="KustoClusterUri" Value="[KustoClusterUri]" />
            <Parameter Name="PrimaryJobsDataStorageAccountName" Value="[PrimaryJobsDataStorageAccountName]" />
            <Parameter Name="WorkspaceDataStorageAccountName" Value="[WorkspaceDataStorageAccountName]" />
            <Parameter Name="DSCPackageStorageAccountNameSuffix" Value="[DSCPackageStorageAccountNameSuffix]" />
            <Parameter Name="ARGEndpoint" Value="[ARGEndpoint]" />
            <Parameter Name="ShortRegionName" Value="[ShortRegionName]" />
          </Section>
        </Settings>
      </ConfigOverride>
    </ConfigOverrides>
  </ServiceManifestImport>
  <ServiceManifestImport>
    <ServiceManifestRef ServiceManifestName="Providers.ServiceFabric.JobRolePkg" ServiceManifestVersion="1.0.0" />
    <ConfigOverrides>
      <ConfigOverride Name="Config">
        <Settings>
          <Section Name="JobRoleConfigSection">
            <Parameter Name="CloudEnvironment" Value="public" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.RoleLocation" Value="[RoleLocation]" />
            <Parameter Name="MainTemplateFilePath_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="[MainTemplateFilePath_Dev]" />
            <Parameter Name="MainTemplateFilePath_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="[MainTemplateFilePath_Staging]" />
            <Parameter Name="MainTemplateFilePath_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="[MainTemplateFilePath_Prod]" />
            <Parameter Name="KeyVault_Endpoint" Value="[KeyVaultEndpoint]" />
            <Parameter Name="MdmAccount" Value="AzureDatabricksRPStgWarm" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FrontdoorEndpointUri" Value="https://management.azure.com/" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ArmMetadataEndpoint" Value="https://admin.management.azure.com/metadata/authentication?api-version=2015-01-01" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.DatabricksApplicationId" Value="02f14ed1-a518-47a4-ac2b-9220f17eae29" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.ServicePrincipalCertificateThumbprint" Value="[AADCertThumbprint]" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.ServicePrincipalCertificateSubjectName" Value="[AADCertSubjectName]" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.AadAllowedAudience" Value="https://management.azure.com/" />
            <Parameter Name="AzureAD_SecretsAppId" Value="02f14ed1-a518-47a4-ac2b-9220f17eae29" />
            <Parameter Name="AzureAD_SecretsTenantId" Value="[TenantId]" />
            <Parameter Name="AzureAD_SecretsLoginTemplate" Value="https://login.microsoftonline.com/" />
            <Parameter Name="PublisherTenantId_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="9f37a392-f0ae-4280-9796-f1864a10effc" />
            <Parameter Name="AuthPrincipalId_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="c464d341-789c-464c-9b1b-6c45bd3d9a4c" />
            <Parameter Name="AuthPrincipalType_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="Group" />
            <Parameter Name="PublisherTenantId_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="72f988bf-86f1-41af-91ab-2d7cd011db47" />
            <Parameter Name="AuthPrincipalId_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="c9b79afc-8394-4a51-83ca-eb5de6046e89" />
            <Parameter Name="AuthPrincipalType_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="Group" />
            <Parameter Name="PublisherTenantId_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="72f988bf-86f1-41af-91ab-2d7cd011db47" />
            <Parameter Name="AuthPrincipalId_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="9a74af6f-d153-4348-988a-e2672920bee9" />
            <Parameter Name="AuthPrincipalType_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="Group" />
            <Parameter Name="MonitoringAgentVersion" Value="1.0" />
            <Parameter Name="MonitoringInitConfig" Value="MdsConfig_Providers.xml" />
            <Parameter Name="Monitoring_GCS_Environment" Value="Test" />
            <Parameter Name="Monitoring_GCS_Account" Value="AzureDatabricksRPStgWarm" />
            <Parameter Name="Monitoring_GCS_Namespace" Value="AzureDatabricksRPStgWarm" />
            <Parameter Name="Monitoring_GCS_Thumbprint" Value="[AADCertThumbprint]" />
            <Parameter Name="Monitoring_Config_Version" Value="21.2" />
            <Parameter Name="Monitoring_Agent_Version" Value="44.4.1" />
            <Parameter Name="Monitoring_Use_Geneva_Config_Service" Value="true" />
            <Parameter Name="SLIMetricsMonitoringAccount" Value="AzureDatabricksRPStgHot" />
            <Parameter Name="SLIMetricsNamespace" Value="SLIMetrics" />
            <Parameter Name="EnableSLIMetricsCapture" Value="true" />
            <Parameter Name="AllowedSLISyncOperations" Value="GET/SUBSCRIPTIONS/RESOURCEGROUPS/PROVIDERS/MICROSOFT.DATABRICKS/WORKSPACES/" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FailHealthCheck" Value="false" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AllowedReadOnlyClientCertificates" Value="" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AllowedAdminCertificates" Value="" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.DefaultRetryInterval" Value="00:00:15" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.AadLoginTemplate" Value="https://login.microsoftonline.com/" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Databricks.Workspace.PublisherPackageIdMappings" Value="2016-09-01-preview#databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12;2017-08-01-preview#databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12;2017-09-01-preview#databricks.databricks-workspace-previewdatabricks-workspace.0.0.2;2018-03-01#databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12;2018-03-15#databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12;2018-04-01#databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Feature.ConfigurationCache.CacheInitializationTimeout" Value="00:01:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServiceClient.RequestTimeout" Value="00:01:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServiceClient.ConnectionLeaseTimeout" Value="00:01:00" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServiceClient.MaxResponseContentSize" Value="8388608" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Feature.TelemetryRequest.TotalEventsLimit" Value="100" />
            <Parameter Name="DatabricksNetworkIntentPoliciesFilePath_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="[DatabricksNetworkIntentPoliciesFilePath_Dev]" />
            <Parameter Name="DatabricksNetworkIntentPoliciesFilePath_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="[DatabricksNetworkIntentPoliciesFilePath_Staging]" />
            <Parameter Name="DatabricksNetworkIntentPoliciesFilePath_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="[DatabricksNetworkIntentPoliciesFilePath_Prod]" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.RegisterManagedByTenantApiVersion" Value="2019-03-01" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.UnregisterManagedByTenantEnabled" Value="false" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Region" Value="[Region]" />
            <Parameter Name="RecurringActions" Value="GetResourceGroupLocks" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.BackgroundJobs.WorkspaceScanJobDefinitionsTableName" Value="workspacescanjobdefinitions" />
            <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.BackgroundJobs.WorkspaceScanJobTriggersQueuePrefix" Value="workspacescanjobtriggers" />
            <Parameter Name="PrimaryJobsDataStorageAccountName" Value="[PrimaryJobsDataStorageAccountName]" />
            <Parameter Name="WorkspaceDataStorageAccountName" Value="[WorkspaceDataStorageAccountName]" />
            <Parameter Name="DSCPackageStorageAccountNameSuffix" Value="[DSCPackageStorageAccountNameSuffix]" />
            <Parameter Name="ARGEndpoint" Value="[ARGEndpoint]" />
            <Parameter Name="ShortRegionName" Value="[ShortRegionName]" />
          </Section>
        </Settings>
      </ConfigOverride>
    </ConfigOverrides>
    <Policies>
      <ServicePackageResourceGovernancePolicy MemoryInMBLimit="4096" />
      <ResourceGovernancePolicy CodePackageRef="JobRoleCodePackage" MemoryInMBLimit="4096" CpuPercent="25" />
    </Policies>
  </ServiceManifestImport>
  <Certificates>
    <EndpointCertificate X509FindType="FindBySubjectName" X509FindValue="[SSLCertSubjectName]" Name="MyCert" />
  </Certificates>
</ApplicationManifest>