﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.BaseClasses
{
    using System.Collections.Generic;

    public class BaseCustomSettings
    {
        private Dictionary<string, string> Settings;

        public BaseCustomSettings()
        {
            Settings = new Dictionary<string, string>
            {
                ["Microsoft.WindowsAzure.ResourceStack.BackgroundJobs.RequestOptionsServerTimeout"] = "00:00:30",
                ["Microsoft.WindowsAzure.ResourceStack.BackgroundJobs.NumWorkersPerProcessorCount"] = "1",
                ["Microsoft.WindowsAzure.ResourceStack.BackgroundJobs.NumPartitionsInJobTriggersQueue"] = "1",
                ["Microsoft.WindowsAzure.ResourceStack.BackgroundJobs.WorkerMinimumSleepInterval"] = "00:00:01",
                ["Microsoft.WindowsAzure.ResourceStack.BackgroundJobs.WorkerMaximumSleepInterval"] = "00:00:02",
                ["Microsoft.WindowsAzure.ResourceStack.SequencerJobs.DefaultSequencerTimeout"] = "00:05:00",
                ["Microsoft.WindowsAzure.ResourceStack.SequencerJobs.DefaultPostponeInterval"] = "00:00:01",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.StartTime.Immediate"] = "00:00:02",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.PostponeInterval"] = "00:00:05",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.RetryInterval"] = "00:00:05",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.SubscriptionDeleteJobJob.PostponeInterval"] = "00:00:05",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.SubscriptionDeleteJobJob.MaxLifetime"] = "00:00:30",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.MaxLifetime"] = "2",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.StartTime.Immediate"] = "00:00:02",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.PostponeInterval"] = "00:00:10",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.Feature.ConfigurationCache.CacheInitializationTimeout"] = "00:01:00",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.Feature.ConfigurationCache.RefreshInterval"] = "00:00:10",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.ServicePointDefaultConnectionLimitPerProcessor"] = "128",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.ServicePointMaxServicePointIdleTime"] = "50000",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.ServicePointUseNagleAlgorithm"] = "false",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.ServicePointExpect100Continue"] = "false",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.ThreadPoolMinWorkerThreads"] = "24",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.ThreadPoolMaxWorkerThreads"] = "48",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.ThreadPoolMinIoThreads"] = "24",

                ["Microsoft.WindowsAzure.ResourceStack.Providers.RoleLocation"] = "DevFabric",
                ["Microsoft.WindowsAzure.Environment.IsCloudEnvironment"] = "true",
                ["Microsoft.WindowsAzure.Environment.IsCloudEnvironmentEmulated"] = "true",
                ["CloudEnvironment"] = "public",
                ["MdmAccount"] = "AzureDatabricksRPWarm",

                ["Microsoft.WindowsAzure.ResourceStack.Providers.Databricks.Workspace.PublisherPackageIdMappings"] = "2018-04-01#databricks.databricks-workspace-previewdatabricks-workspace.0.0.2",
                ["MainTemplateFilePath_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2"] = ".\\DatabricksTemplates\\Local\\prodMainTemplate.json",

                ["PublisherTenantId_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2"] = "3667c757-a1ad-4283-8ed0-ac282de4092b",
                ["AuthPrincipalType_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2"] = "Group",
                ["AuthPrincipalId_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2"] = "3876a311-45ef-47a0-b48d-da8564b4d3c4",
                ["AuthRoleDefinitionId_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2"] = "83dd7dee-e093-40f1-974b-110dfa9cea51",
                ["GetNetworkIntentPoliciesAllowList"] = "2cf9eb86-36b5-49dc-86ae-9a63135dfa8c",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.DatabricksApplicationId"] = "797f4846-ba00-4fd7-ba43-dac1f8f63013",

                ["Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.AadAllowedAudience"] = "https://management.azure.com/",
                ["AzureAD_SecretsAppId"] = "5",
                ["AzureAD_SecretsLoginTemplate"] = "http://localhost",
                ["AzureAD_SecretsTenantId"] = "5",

                ["StorageApiVersion"] = "2022-09-01",
                ["FeaturesApiVersion"] = "2015-12-01",
                ["DenyAssignmentsApiVersion"] = "2019-03-01-preview",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.RegisterManagedByTenantApiVersion"] = "2018-07-01",

                ["Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.UnregisterManagedByTenantEnabled"] = "false",

                ["Microsoft.WindowsAzure.ResourceStack.Providers.ArmMetadataEndpoint"] = "http://localhost:8888",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.FrontdoorEndpointUri"] = Constants.EndPoints.FrontDoorHost,
                ["Microsoft.WindowsAzure.ResourceStack.Providers.ManagedServices.AadLoginTemplate"] = "http://localhost:8002",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.AadLoginTemplate"] = "http://localhost:8002",
                ["DatabricksAccountsMgrDevStagingDnsTemplate"] = Constants.EndPoints.FrontDoorHost,
                ["DatabricksAccountsMgrDns"] = Constants.EndPoints.FrontDoorHost,
                ["ClientSettingsProvider.ServiceUri"] = "",

                ["Microsoft.WindowsAzure.ResourceStack.Providers.AllowedReadOnlyClientCertificates"] = "",
                ["Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.ServicePrincipalCertificateSubjectName"] = "dummycert.azurewebsites.net",
                ["VirtualNetworkInjectionOperationsApiVersion"] = "2019-06-01",
                ["DatabricksNetworkIntentPoliciesFilePath_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2"] = ".\\DatabricksTemplates\\Local\\prodDatabricksNIPMappings.json",
                ["PrimaryJobsDataStorageAccountName"] = "eastus2euapjobsdatav3",
                ["SecondaryJobsDataStorageAccountName"] = "eastus2euapjobsdatav4",
                ["WorkspaceDataStorageAccountName"] = "eastus2euapwsdatav2",
                ["AuthPrincipalType_2018-04-01"] = "Group",
                ["VNetInjectionMandatoryApiVersions"] = "2025-02-01-preview;2025-06-01-preview",

                ["AllowedDevLocationsForNoAzureServiceRules"] = "japaneast;koreacentral",
                ["AllowedStagingLocationsForNoAzureServiceRules"] = "NA",
                ["AllowedProdLocationsForNoAzureServiceRules"] = "eastus2euap;centralus;eastus;australiaeast;eastus2;northeurope;westus2;westeurope"
            };
        }

        public virtual void UpdateSettings()
        {

        }

        public IDictionary<string, string> GetSettings()
        {
            return Settings;
        }
    }
}
