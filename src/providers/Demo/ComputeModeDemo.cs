//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.DBAsyncContract;
using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
using Newtonsoft.Json;
using System;

namespace Demo
{
    /// <summary>
    /// Demonstration of how computeMode property appears in account API payload
    /// </summary>
    public class ComputeModeDemo
    {
        public static void Main()
        {
            Console.WriteLine("=== Databricks Account API ComputeMode Demo ===\n");

            // Create a sample ApplianceEntity with Serverless compute mode
            var serverlessEntity = new ApplianceEntity
            {
                Name = "cong-test-serverless-********",
                Location = "eastus2euap",
                SubscriptionId = "653bb673-e55d-452c-a90b-d064d5d53ca4",
                ResourceGroup = "cong-serverless-test",
                Properties = new ApplicationProperties
                {
                    ComputeMode = ComputeMode.Serverless,
                    WorkspaceId = "***************",
                    WorkspaceUrl = "adb-***************.12.azuredatabricks.net",
                    ManagedResourceGroupId = "/subscriptions/653bb673-e55d-452c-a90b-d064d5d53ca4/resourceGroups/databricks-rg-cong-test-serverless-********-abc123"
                }
            };

            // Convert to DatabricksWorkspace (account API format)
            var databricksWorkspace = DatabricksWorkspace.FromApplianceEntity(serverlessEntity);

            // Serialize to JSON to show the account API payload
            var json = JsonConvert.SerializeObject(databricksWorkspace, Formatting.Indented);

            Console.WriteLine("Serverless Workspace Account API Payload:");
            Console.WriteLine(json);
            Console.WriteLine("\n" + new string('=', 60) + "\n");

            // Create a sample ApplianceEntity with Hybrid compute mode
            var hybridEntity = new ApplianceEntity
            {
                Name = "cong-test-hybrid-workspace",
                Location = "eastus2euap",
                SubscriptionId = "653bb673-e55d-452c-a90b-d064d5d53ca4",
                ResourceGroup = "cong-hybrid-test",
                Properties = new ApplicationProperties
                {
                    ComputeMode = ComputeMode.Hybrid,
                    WorkspaceId = "***************",
                    WorkspaceUrl = "adb-***************.12.azuredatabricks.net",
                    ManagedResourceGroupId = "/subscriptions/653bb673-e55d-452c-a90b-d064d5d53ca4/resourceGroups/databricks-rg-cong-test-hybrid-workspace-def456"
                }
            };

            // Convert to DatabricksWorkspace (account API format)
            var hybridDatabricksWorkspace = DatabricksWorkspace.FromApplianceEntity(hybridEntity);

            // Serialize to JSON to show the account API payload
            var hybridJson = JsonConvert.SerializeObject(hybridDatabricksWorkspace, Formatting.Indented);

            Console.WriteLine("Hybrid Workspace Account API Payload:");
            Console.WriteLine(hybridJson);
            Console.WriteLine("\n" + new string('=', 60) + "\n");

            // Show just the properties section to highlight the computeMode
            Console.WriteLine("Properties section with computeMode:");
            var propertiesJson = JsonConvert.SerializeObject(databricksWorkspace.Properties, Formatting.Indented);
            Console.WriteLine(propertiesJson);
        }
    }
}
