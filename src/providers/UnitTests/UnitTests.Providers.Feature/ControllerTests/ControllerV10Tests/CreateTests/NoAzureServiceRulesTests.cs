﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV10Tests
{
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.Net;
    using System.Threading.Tasks;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV7;
    using Moq;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public class NoAzureServiceRulesTests : BaseControllerV10Test
    {
        protected override JToken Sku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Premium""}");
            }
        }

        private WorkspacePropertiesV7 incomingWorkspaceProperties;
        protected override WorkspacePropertiesV7 IncomingWorkspaceProperties
        {
            get
            {
                if (incomingWorkspaceProperties == null)
                {
                    incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                }

                return incomingWorkspaceProperties;
            }
        }

        private WorkspaceV7 incomingWorkspace;
        protected override WorkspaceV7 IncomingWorkspace
        {
            get
            {
                if (incomingWorkspace == null)
                {
                    incomingWorkspace = base.IncomingWorkspace;
                    incomingWorkspace.Properties = this.incomingWorkspaceProperties;
                    incomingWorkspace.Sku = this.Sku;
                }

                return incomingWorkspace;
            }
        }

        #region Mock front door for checking if subscription is registered for feature
        private Mock<IFrontdoorEngine> frontdoorEngineMock;
        protected override Mock<IFrontdoorEngine> FrontdoorEngineMock
        {
            get
            {
                if (frontdoorEngineMock == null)
                {
                    frontdoorEngineMock = base.FrontdoorEngineMock;

                    var subscriptionRegistered = new FeatureDefinition
                    {
                        Properties = JToken.Parse("{\"state\": \"Registered\"}")
                    };

                    this.frontdoorEngineMock.Setup<Task<FeatureDefinition>>(o => o.GetFeatureRegistrationByName(
                            It.Is<string>(tenantId => tenantId == Constants.Common.HomeTenantId),
                            It.Is<string>(subId => subId == Constants.Common.SubscriptionId),
                            It.Is<string>(rpNamespace => rpNamespace == Constants.Common.ResourceProviderNamespace),
                            It.Is<string>(featureName =>
                                featureName == ProviderConstants.Databricks.AllowNoAzureServiceRules)
                        ))
                        .ReturnsAsync(subscriptionRegistered);
                }

                return this.frontdoorEngineMock;
            }
        }
        #endregion

        #region Arrange the configuration settings
        private Dictionary<string, string> configurationSettings;
        protected virtual Dictionary<string, string> ConfigurationSettings
        {
            get
            {
                if (configurationSettings == null)
                {
                    configurationSettings = new Dictionary<string, string>
                    {
                        { "EnableDBWorkspaceNotification", "true" },
                        { "DisableDBWorkspaceFeatureFlagNotificationCheck", "true" },
                        { $"MainTemplateFilePath_{Constants.Common.PublisherPackageId}", Constants.Common.ProdMainTemplate },
                        { $"MainTemplateFilePath_{Constants.Common.DevPublisherPackageId}", Constants.Common.DevMainTemplate },
                        { $"MainTemplateFilePath_{Constants.Common.StagingPublisherPackageId}", Constants.Common.StagingMainTemplate },
                        { "PublisherTenantId_" + Constants.Common.PublisherPackageId, Constants.Common.PublisherTenantId },
                        { "PublisherTenantId_" + Constants.Common.DevPublisherPackageId, Constants.Common.DevPublisherTenantId },
                        { "PublisherTenantId_" + Constants.Common.StagingPublisherPackageId, Constants.Common.StagingPublisherTenantId },
                        { "AuthPrincipalId_" + Constants.Common.PublisherPackageId, Constants.Common.AuthorizationPrincipalId },
                        { "AuthPrincipalId_" + Constants.Common.DevPublisherPackageId, Constants.Common.DevAuthorizationPrincipalId },
                        { "AuthPrincipalId_" + Constants.Common.StagingPublisherPackageId, Constants.Common.StagingAuthorizationPrincipalId },
                        { "CloudEnvironment", "public"},
                        { "AllowedProdLocationsForNoAzureServiceRules", "eastus2euap;centralus;eastus;australiaeast;eastus2;northeurope;westus2;westeurope"},
                        { "AllowedDevLocationsForNoAzureServiceRules", "japaneast;koreacentral" }
                    };
                }

                return configurationSettings;
            }
        }
        #endregion

        [TestMethod("Workspace creation with NoAzureServiceRule in invalid region should fail")]
        public async Task CreateWorkspaceWithNoAzureServiceRuleInValidRegion()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;

                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId1 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));

                this.IncomingWorkspaceProperties.PublicNetworkAccess = PublicNetworkAccessStatus.Disabled;
                this.IncomingWorkspaceProperties.RequiredNsgRules =
                    RequiredNetworkSecurityGroupType.NoAzureServiceRules;

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV10Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.NoAzureServiceRuleNotSupportedInRegion, ex.ErrorCode);
            }
        }
    }
}
