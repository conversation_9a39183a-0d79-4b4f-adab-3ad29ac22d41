﻿
using System;
using System.Net;
using System.Net.Http;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
using Microsoft.WindowsAzure.ResourceStack.Common.Json;
using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.DataProviders;
using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Metadata;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
using Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Engines;
using Moq;
using Newtonsoft.Json.Linq;

namespace UnitTests.Providers.Worker.JobHandlerTests.ProvisioningJobHandler.UpdatingHandler.ConfigureDefaultStorageFirewall
{
    [TestClass]
    public class HandleDefaultStorageFirewallConfigurationTests : BaseWorkspaceInitializingTest
    {
        private ICommonEventSource _eventSource;
        private IFrontdoorEngine _frontdoorEngine;
        private IDatabricksBlobDataProvider _databricksBlobDataProvider;
        private IAccessConnectorDataProvider _accessConnectorDataProvider;
        private IApplianceDataProvider _applianceDataProvider;
        private ApplianceJobMetadata _metadata;
        private IApplianceEngine _applianceEngine;

        private InsensitiveDictionary<JToken> incomingWorkspaceParameters;
        protected override InsensitiveDictionary<JToken> IncomingWorkspaceParameters
        {
            get
            {
                if (incomingWorkspaceParameters == null)
                {
                    incomingWorkspaceParameters = new InsensitiveDictionary<JToken>
                    {
                        { ProviderConstants.Databricks.StorageAccountNameParameter, JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.StorageAccountName + @"""}")},
                        { ProviderConstants.Databricks.CustomVirtualNetworkIdProperty, JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.VNetId + @"""}") },
                        { ProviderConstants.Databricks.CustomPublicSubnetNameProperty, JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.CustomPublicSubnetName + @"""}") },
                        { ProviderConstants.Databricks.CustomPrivateSubnetNameProperty, JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.CustomPrivateSubnetName + @"""}") },
                        { "vnetAddressPrefix", JToken.Parse(@"{ ""type"": ""String"", ""value"": ""10.139""}") },
                        { "resourceTags", JToken.Parse(@"{ ""type"": ""Object"", ""value"":  { ""application"": ""databricks"", ""databricks-environment"": ""true"" }}") }
                    };
                }

                return incomingWorkspaceParameters;
            }
        }

        [TestInitialize]
        public void TestInitialize()
        {
            _frontdoorEngine = this.FrontdoorEngine.Object;
            _databricksBlobDataProvider = new Mock<IDatabricksBlobDataProvider>().Object;
            _accessConnectorDataProvider = this.AccessConncetorDataProvider.Object;
            _applianceDataProvider = this.ApplianceDataProvider.Object;
            _metadata = new ApplianceJobMetadata();
            _eventSource = new Mock<ICommonEventSource>().Object;

            _applianceEngine = new ApplianceEngine(
                eventSource: _eventSource,
                frontdoorEngine: _frontdoorEngine,
                databricksBlobDataProvider: _databricksBlobDataProvider,
                accessConnectorDataProvider: _accessConnectorDataProvider,
                applianceDataProvider: _applianceDataProvider,
                metadata: _metadata);

            this.Metadata.ResourceOperation = ProvisioningOperation.Updating;
            this.IncomingWorkspaceProperties.Parameters = this.IncomingWorkspaceParameters;
            this.IncomingApplianceEntity.Properties.AccessConnector = new AccessConnectorIdEntity
            {
                Id = Constants.AccessConnectorId,
                PrincipalId = Constants.SystemAssignedIdentity.PrincipalId,
                TenantId = Constants.SystemAssignedIdentity.TenantId
            };

            this.IncomingApplianceEntity.Properties.DefaultStorageFirewall = DefaultStorageFirewall.Enabled;

            this.Metadata.Parameters = new InsensitiveDictionary<JToken>
            {
                { ProviderConstants.WorkspaceUpdateParameters.Properties, this.IncomingWorkspaceProperties?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Sku, this.IncomingEntitySku?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Tags, this.IncomingApplianceEntity?.Tags?.ToJToken() }
            };

            this.ApplianceEntity.Properties.Parameters = incomingWorkspaceParameters;

            var response = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(@"{ ""storageAccountName"": """ + Constants.StorageAccountName + @"""}")
            };

            this.FrontdoorEngine.Setup(
                        o => o.PatchStorageResource(
                            It.IsAny<Uri>(),
                            ProviderConstants.Databricks.ResourceProviderNamespace,
                            Constants.PublisherTenantId,
                            It.IsAny<StorageDefinition>()
                            )).ReturnsAsync(response);

            AccessConnectorIdEntity accessConnectorIdEntity = new AccessConnectorIdEntity
            {
                TenantId = Constants.AccessConnectorId,
                Id = Constants.AccessConnectorId
            };
        }

        [TestMethod]
        public void HandleDefaultStorageFirewallConfiguration_Enable()
        {
            this.IncomingApplianceEntity.Properties.DefaultStorageFirewall = DefaultStorageFirewall.Enabled;
            this.IncomingApplianceEntity.Properties.AccessConnector = new AccessConnectorIdEntity
            {
                Id = Constants.AccessConnectorId,
                PrincipalId = Constants.SystemAssignedIdentity.PrincipalId,
                TenantId = Constants.SystemAssignedIdentity.TenantId
            };
            this.ApplianceEntity.Properties.DefaultStorageFirewall = null;

            var task = _applianceEngine.ConfigureDefaultStorageFirewall(
                                        this.IncomingApplianceEntity,
                                        this.ApplianceEntity.Properties.AccessConnector,
                                        this.ApplianceEntity.Properties.DefaultStorageFirewall,
                                        Constants.PublisherTenantId,
                                        this.ApplianceEntity.Properties.ManagedResourceGroupId);

            Assert.AreEqual(DefaultStorageFirewall.Enabled, task.Result.Item3);
            Assert.AreEqual(Constants.AccessConnectorId, task.Result.Item2.Id);
        }

        [TestMethod("Test the scenario where the workspace is UC enabled, the defaultStoragefirewall was enabled and then disabled, and then enabled again")]
        public void HandleDefaultStorageFirewallConfiguration_Enable_Disable_Enable()
        {
            this.IncomingApplianceEntity.Properties.DefaultStorageFirewall = DefaultStorageFirewall.Enabled;
            this.IncomingApplianceEntity.Properties.AccessConnector = new AccessConnectorIdEntity
            {
                Id = Constants.AccessConnectorId,
                PrincipalId = Constants.SystemAssignedIdentity.PrincipalId,
                TenantId = Constants.SystemAssignedIdentity.TenantId
            };
            this.ApplianceEntity.Properties.DefaultStorageFirewall = null;

            var task = _applianceEngine.ConfigureDefaultStorageFirewall(
                                        this.IncomingApplianceEntity,
                                        this.ApplianceEntity.Properties.AccessConnector,
                                        this.ApplianceEntity.Properties.DefaultStorageFirewall,
                                        Constants.PublisherTenantId,
                                        this.ApplianceEntity.Properties.ManagedResourceGroupId);

            Assert.AreEqual(DefaultStorageFirewall.Enabled, task.Result.Item3);
            Assert.AreEqual(Constants.AccessConnectorId, task.Result.Item2.Id);

            this.IncomingApplianceEntity.Properties.DefaultStorageFirewall = DefaultStorageFirewall.Disabled;
            this.ApplianceEntity.Properties.DefaultStorageFirewall = DefaultStorageFirewall.Enabled;
            this.ApplianceEntity.Properties.AccessConnector = new AccessConnectorIdEntity
            {
                Id = Constants.AccessConnectorId,
                PrincipalId = Constants.SystemAssignedIdentity.PrincipalId,
                TenantId = Constants.SystemAssignedIdentity.TenantId
            };

            task = _applianceEngine.ConfigureDefaultStorageFirewall(
                                        this.IncomingApplianceEntity,
                                        this.ApplianceEntity.Properties.AccessConnector,
                                        this.ApplianceEntity.Properties.DefaultStorageFirewall,
                                        Constants.PublisherTenantId,
                                        this.ApplianceEntity.Properties.ManagedResourceGroupId);

            Assert.AreEqual(DefaultStorageFirewall.Disabled, task.Result.Item3);
            Assert.AreEqual(Constants.AccessConnectorId, task.Result.Item2.Id);

            this.IncomingApplianceEntity.Properties.DefaultStorageFirewall = DefaultStorageFirewall.Enabled;
            this.ApplianceEntity.Properties.DefaultStorageFirewall = DefaultStorageFirewall.Disabled;
            this.ApplianceEntity.Properties.AccessConnector = new AccessConnectorIdEntity
            {
                Id = Constants.AccessConnectorId,
                PrincipalId = Constants.SystemAssignedIdentity.PrincipalId,
                TenantId = Constants.SystemAssignedIdentity.TenantId
            };

            task = _applianceEngine.ConfigureDefaultStorageFirewall(
                                        this.IncomingApplianceEntity,
                                        this.ApplianceEntity.Properties.AccessConnector,
                                        this.ApplianceEntity.Properties.DefaultStorageFirewall,
                                        Constants.PublisherTenantId,
                                        this.ApplianceEntity.Properties.ManagedResourceGroupId);

            Assert.AreEqual(DefaultStorageFirewall.Enabled, task.Result.Item3);
            Assert.AreEqual(Constants.AccessConnectorId, task.Result.Item2.Id);

            this.FrontdoorEngine.Verify(
                o => o.PatchStorageResource(
                    It.IsAny<Uri>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<StorageDefinition>()));
        }

        /// <summary>
        /// Test the scenario where the workspace is not UC enabled, the defaultStoragefirewall was enabled and then disabled.
        /// The access connector is null. In that case, other network settings update should work.
        /// </summary>
        [TestMethod]
        public void HandleDefaultStorageFirewallConfiguration_NullAccessConnector()
        {
            this.IncomingApplianceEntity.Properties.DefaultStorageFirewall = DefaultStorageFirewall.Disabled;
            this.ApplianceEntity.Properties.DefaultStorageFirewall = DefaultStorageFirewall.Disabled;
            this.ApplianceEntity.Properties.AccessConnector = null;

            var task = _applianceEngine.ConfigureDefaultStorageFirewall(
                                        this.IncomingApplianceEntity,
                                        this.ApplianceEntity.Properties.AccessConnector,
                                        this.ApplianceEntity.Properties.DefaultStorageFirewall,
                                        Constants.PublisherTenantId,
                                        this.ApplianceEntity.Properties.ManagedResourceGroupId);

            Assert.AreEqual(DefaultStorageFirewall.Disabled, task.Result.Item3);
        }

        [TestMethod]
        public void HandleDefaultStorageFirewallConfiguration_Disable()
        {
            this.IncomingApplianceEntity.Properties.DefaultStorageFirewall = DefaultStorageFirewall.Disabled;
            this.ApplianceEntity.Properties.DefaultStorageFirewall = DefaultStorageFirewall.Enabled;
            this.ApplianceEntity.Properties.AccessConnector = new AccessConnectorIdEntity
            {
                Id = Constants.AccessConnectorId,
                PrincipalId = Constants.SystemAssignedIdentity.PrincipalId,
                TenantId = Constants.SystemAssignedIdentity.TenantId
            };

            var task = _applianceEngine.ConfigureDefaultStorageFirewall(
                                        this.IncomingApplianceEntity,
                                        this.ApplianceEntity.Properties.AccessConnector,
                                        this.ApplianceEntity.Properties.DefaultStorageFirewall,
                                        Constants.PublisherTenantId,
                                        this.ApplianceEntity.Properties.ManagedResourceGroupId);

            Assert.AreEqual(DefaultStorageFirewall.Disabled, task.Result.Item3);
        }

        [TestMethod]
        public void HandleDefaultStorageFirewallConfiguration_UpdateAccessConnector()
        {
            this.IncomingApplianceEntity.Properties.DefaultStorageFirewall = DefaultStorageFirewall.Enabled;
            this.IncomingApplianceEntity.Properties.AccessConnector = new AccessConnectorIdEntity
            {
                Id = Constants.AccessConnectorId,
                PrincipalId = Constants.SystemAssignedIdentity.PrincipalId,
                TenantId = Constants.SystemAssignedIdentity.TenantId
            };
            this.ApplianceEntity.Properties.DefaultStorageFirewall = DefaultStorageFirewall.Enabled;
            this.ApplianceEntity.Properties.AccessConnector = new AccessConnectorIdEntity
            {
                Id = Constants.AccessConnectorId + "0",
                PrincipalId = Constants.SystemAssignedIdentity.PrincipalId,
                TenantId = Constants.SystemAssignedIdentity.TenantId
            };

            var task = _applianceEngine.ConfigureDefaultStorageFirewall(
                            this.IncomingApplianceEntity,
                            this.ApplianceEntity.Properties.AccessConnector,
                            this.ApplianceEntity.Properties.DefaultStorageFirewall,
                            Constants.PublisherTenantId,
                            this.ApplianceEntity.Properties.ManagedResourceGroupId);

            Assert.AreEqual(DefaultStorageFirewall.Enabled, task.Result.Item3);
            Assert.AreEqual(Constants.AccessConnectorId, task.Result.Item2.Id);
        }

        [TestMethod]
        public void HandleDefaultStorageFirewallConfiguration_NoConfiguration()
        {
            this.IncomingApplianceEntity.Properties.DefaultStorageFirewall = null;
            this.ApplianceEntity.Properties.DefaultStorageFirewall = null;

            var task = _applianceEngine.ConfigureDefaultStorageFirewall(
                                        this.IncomingApplianceEntity,
                                        this.ApplianceEntity.Properties.AccessConnector,
                                        this.ApplianceEntity.Properties.DefaultStorageFirewall,
                                        Constants.PublisherTenantId,
                                        this.ApplianceEntity.Properties.ManagedResourceGroupId);

            Assert.IsNull(task.Result.Item1);
        }
    }
}
