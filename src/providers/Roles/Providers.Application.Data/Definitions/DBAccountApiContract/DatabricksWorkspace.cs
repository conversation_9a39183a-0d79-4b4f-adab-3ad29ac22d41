﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.DBAsyncContract
{
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.DBCSContract;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.ManagedIdentity;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Newtonsoft.Json;
    using System.Collections.Generic;

    /// <summary>
    /// Represents a Databricks workspace with support for asynchronous operations.
    /// This class extends the base Appliance class to provide specialized properties
    /// and methods for asynchronous Databricks workspace operations.
    /// </summary>
    public class DatabricksWorkspace : Appliance
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="DatabricksWorkspace"/> class.
        /// Sets the default type to "Microsoft.Databricks/workspaces".
        /// </summary>
        public DatabricksWorkspace(): base()
        {
            base.Type = "Microsoft.Databricks/workspaces";
        }

        /// <summary>
        /// Gets or sets the asynchronous workspace properties including operation status,
        /// workspace state, and other specialized properties for Databricks workspaces.
        /// </summary>
        [JsonProperty("properties", Required = Required.Default)]
        public new AsyncWorkspaceProperties Properties { get; set; }

        /// <summary>
        /// Converts an ApplianceEntity to AsyncDBWorkspace.
        /// Maps all properties from the source entity to their corresponding properties
        /// in AsyncDBWorkspace, handling special cases like encryption settings and parameters.
        /// </summary>
        /// <param name="appliance">The ApplianceEntity to convert from. If null, returns null.</param>
        /// <returns>A new AsyncDBWorkspace instance populated from the ApplianceEntity with appropriate default values for missing properties.</returns>
        public static DatabricksWorkspace FromApplianceEntity(ApplianceEntity appliance)
        {
            if (appliance == null)
            {
                return null;
            }

            var asyncWorkspace = new DatabricksWorkspace
            {
                SubscriptionId = appliance.SubscriptionId,
                ResourceGroup = appliance.ResourceGroup,
                FullyQualifiedName = appliance.Name,
                Location = appliance.Location,
                Tags = appliance.Tags,
                Sku = appliance.Sku,
                Properties = new AsyncWorkspaceProperties
                {
                    // Initialize with default values
                    OperationStatus = new DBOperationStatusObject { OperationStatus = DBOperationStatus.Running },
                    DBWorkspaceState = DBWorkspaceState.Inactive,
                    DefaultCatalog = new DefaultCatalog
                    {
                        InitialType = CatalogType.UnityCatalog,
                        InitialName = ""
                    },
                    IsUcEnabled = false
                }
            };

            // Copy properties from ApplianceEntity.Properties to AsyncWorkspaceProperties if they exist
            if (appliance.Properties != null)
            {
                var appProps = appliance.Properties;

                // Copy properties directly from ApplicationProperties
                // Only copy properties that exist in both ApplicationProperties and AsyncWorkspaceProperties
                asyncWorkspace.Properties.DiskEncryptionSetId = appProps.DiskEncryptionSetId;
                asyncWorkspace.Properties.PublicNetworkAccess = appProps.PublicNetworkAccess;
                asyncWorkspace.Properties.RequiredNsgRules = appProps.RequiredNsgRules;
                asyncWorkspace.Properties.DefaultStorageFirewall = appProps.DefaultStorageFirewall;
                asyncWorkspace.Properties.EnhancedSecurityCompliance = appProps.EnhancedSecurityCompliance;

                // Handle encryption properties conversion
                if (appProps.EncryptionProperties != null)
                {
                    var encryption = new Encryption();

                    // Map encryption entities if they exist
                    if (appProps.EncryptionProperties.EncryptionEntities != null)
                    {
                        encryption.Entities = new EncryptionEntities();

                        // Map managed services if they exist
                        if (appProps.EncryptionProperties.EncryptionEntities.ManagedServices != null)
                        {
                            encryption.Entities.ManagedServices = new ManagedServices
                            {
                                KeySource = appProps.EncryptionProperties.EncryptionEntities.ManagedServices.KeySource
                            };

                            // Map key vault properties if they exist
                            if (appProps.EncryptionProperties.EncryptionEntities.ManagedServices.KeyVaultProperties != null)
                            {
                                encryption.Entities.ManagedServices.KeyVaultProperties = new ManagedServicesKeyVault
                                {
                                    KeyName = appProps.EncryptionProperties.EncryptionEntities.ManagedServices.KeyVaultProperties.KeyName,
                                    KeyVersion = appProps.EncryptionProperties.EncryptionEntities.ManagedServices.KeyVaultProperties.KeyVersion,
                                    KeyVaultUri = appProps.EncryptionProperties.EncryptionEntities.ManagedServices.KeyVaultProperties.KeyVaultUri
                                };
                            }
                        }
                    }

                    asyncWorkspace.Properties.Encryption = encryption;
                }

                // Parameters might need conversion depending on their structure
                if (appProps.Parameters != null)
                {
                    // Create a new WorkspaceParameters instance and map the relevant parameters
                    var workspaceParams = new WorkspaceParameters();

                    // Extract known parameters if they exist in the ApplicationProperties.Parameters dictionary
                    if (appProps.Parameters.TryGetValue("customVirtualNetworkId", out var customVNetId))
                    {
                        workspaceParams.CustomVirtualNetworkId = customVNetId;
                    }

                    if (appProps.Parameters.TryGetValue("customPublicSubnetName", out var pubSubnet))
                    {
                        workspaceParams.CustomPublicSubnetName = pubSubnet;
                    }

                    if (appProps.Parameters.TryGetValue("customPrivateSubnetName", out var privSubnet))
                    {
                        workspaceParams.CustomPrivateSubnetName = privSubnet;
                    }

                    if (appProps.Parameters.TryGetValue("amlWorkspaceId", out var amlId))
                    {
                        workspaceParams.AmlWorkspaceId = amlId;
                    }

                    if (appProps.Parameters.TryGetValue("enableNoPublicIp", out var enableNoIp))
                    {
                        workspaceParams.EnableNoPublicIp = enableNoIp;
                    }

                    if (appProps.Parameters.TryGetValue("encryption", out var encryption))
                    {
                        workspaceParams.Encryption = encryption;
                    }

                    if (appProps.Parameters.TryGetValue("loadBalancerBackendPoolName", out var lbBackendPoolName))
                    {
                        workspaceParams.LoadBalancerBackendPoolName = lbBackendPoolName;
                    }

                    if (appProps.Parameters.TryGetValue("loadBalancerId", out var loadBalancerId))
                    {
                        workspaceParams.LoadBalancerId = loadBalancerId;
                    }

                    if (appProps.Parameters.TryGetValue("natGatewayName", out var natGatewayName))
                    {
                        workspaceParams.NatGatewayName = natGatewayName;
                    }

                    if (appProps.Parameters.TryGetValue("prepareEncryption", out var prepareEncryption))
                    {
                        workspaceParams.PrepareEncryption = prepareEncryption;
                    }

                    if (appProps.Parameters.TryGetValue("publicIpName", out var publicIpName))
                    {
                        workspaceParams.PublicIpName = publicIpName;
                    }

                    if (appProps.Parameters.TryGetValue("requireInfrastructureEncryption", out var requireInfraEncryption))
                    {
                        workspaceParams.RequireInfrastructureEncryption = requireInfraEncryption;
                    }

                    if (appProps.Parameters.TryGetValue("resourceTags", out var resourceTags))
                    {
                        workspaceParams.ResourceTags = resourceTags;
                    }

                    if (appProps.Parameters.TryGetValue("storageAccountName", out var storageAccName))
                    {
                        workspaceParams.StorageAccountName = storageAccName;
                    }

                    if (appProps.Parameters.TryGetValue("storageAccountSkuName", out var storageAccSkuName))
                    {
                        workspaceParams.StorageAccountSkuName = storageAccSkuName;
                    }

                    if (appProps.Parameters.TryGetValue("vnetAddressPrefix", out var vnetAddressPrefix))
                    {
                        workspaceParams.VNetAddressPrefix = vnetAddressPrefix;
                    }

                    asyncWorkspace.Properties.Parameters = workspaceParams;
                }

                // Handle properties that might have different types
                if (appProps.AccessConnector != null)
                {
                    // Convert AccessConnectorIdEntity to AccessConnectorId
                    asyncWorkspace.Properties.AccessConnector = new AccessConnectorId
                    {
                        Id = appProps.AccessConnector.Id,
                        IdentityType = appProps.AccessConnector.IdentityType,
                        UserAssignedIdentityId = appProps.AccessConnector.UserAssignedIdentityId
                    };
                }

                // Copy additional properties from AsyncWorkspaceProperties that exist in ApplicationProperties
                asyncWorkspace.Properties.WorkspaceId = appProps.WorkspaceId;
                asyncWorkspace.Properties.WorkspaceUrl = appProps.WorkspaceUrl;
                asyncWorkspace.Properties.CreatedBy = appProps.CreatedBy;
                asyncWorkspace.Properties.UpdatedBy = appProps.UpdatedBy;
                asyncWorkspace.Properties.ProvisioningState = appProps.ProvisioningState;
                asyncWorkspace.Properties.Authorizations = appProps.Authorizations;
                asyncWorkspace.Properties.ManagedResourceGroupId = appProps.ManagedResourceGroupId;

                // Set DefaultCatalog from ApplicationProperties if it exists
                if (appProps.DefaultCatalog != null)
                {
                    asyncWorkspace.Properties.DefaultCatalog = appProps.DefaultCatalog;

                    // Add null check before accessing HasValue property
                    if (appProps.IsUcEnabled != null && appProps.IsUcEnabled.HasValue)
                    {
                        asyncWorkspace.Properties.IsUcEnabled = appProps.IsUcEnabled.Value;
                    }
                    // If IsUcEnabled is null, we'll use the default value (false) that was set earlier
                }

                // Set ComputeMode using the GetComputeMode extension method
                asyncWorkspace.Properties.ComputeMode = appliance.GetComputeMode();
            }

            // Set a default Identity if needed
            if (asyncWorkspace.Identity == null)
            {
                asyncWorkspace.Identity = new Identity
                {
                    Type = IdentityType.None
                };
            }

            return asyncWorkspace;
        }

        /// <summary>
        /// Converts the AsyncDBWorkspace to a WorkspaceDetails instance for API responses.
        /// Maps essential properties like WorkspaceId, WorkspaceURL, and IsUcEnabled while
        /// setting appropriate defaults for API-specific properties.
        /// </summary>
        /// <returns>The WorkspaceDetails object with all properties correctly mapped from this AsyncDBWorkspace,
        /// or an empty WorkspaceDetails instance if Properties is null.</returns>
        public WorkspaceDetails ToWorkspaceDetails()
        {
            // Create the WorkspaceDetails object with all required properties
            var workspaceDetails = new WorkspaceDetails();

            // Check if properties are null to avoid NullReferenceException
            if (this.Properties == null)
            {
                return workspaceDetails;
            }

            // Map the properties from AsyncWorkspaceProperties
            workspaceDetails.WorkspaceId = this.Properties.WorkspaceId;
            workspaceDetails.WorkspaceURL = this.Properties.WorkspaceUrl;
            workspaceDetails.IsUcEnabled = this.Properties.IsUcEnabled;

            // Initialize PrivateLinkAssets as empty list since AsyncWorkspaceProperties doesn't have this property
            workspaceDetails.PrivateLinkAssets = new List<string>();

            // Set IsPrivateLinkAllowed to false by default since AsyncDBWorkspace doesn't have Metadata property
            workspaceDetails.IsPrivateLinkAllowed = false;

            return workspaceDetails;
        }
    }
}
