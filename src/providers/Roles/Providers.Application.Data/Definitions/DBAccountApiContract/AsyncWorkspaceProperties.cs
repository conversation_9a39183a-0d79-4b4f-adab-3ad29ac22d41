﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.DBAsyncContract
{
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.DBCSContract;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Newtonsoft.Json;
    using System;
    /// <summary>
    /// Represents the properties of an asynchronous Databricks workspace.
    /// Extends the base WorkspaceProperties class with additional properties
    /// related to asynchronous operations and state management.
    /// </summary>
    public class AsyncWorkspaceProperties : WorkspaceProperties
    {
        /// <summary>
        /// Gets or sets the managed resource group Id.
        /// This is the Azure resource ID of the resource group that contains
        /// all the Azure resources associated with the Databricks workspace.
        /// </summary>
        [JsonProperty("managedResourceGroupId", Required = Required.Default)]
        public string ManagedResourceGroupId { get; set; }

        /// <summary>
        /// Gets or sets the creation date time.
        /// The timestamp when the workspace was initially created.
        /// </summary>
        [JsonProperty("createdDateTime", Required = Required.Default)]
        public DateTime? CreatedDateTime { get; set; }

        /// <summary>
        /// Gets or sets the workspace id.
        /// A unique identifier for the Databricks workspace within the Databricks control plane.
        /// </summary>
        [JsonProperty("workspaceId", Required = Required.Default)]
        public string WorkspaceId { get; set; }

        /// <summary>
        /// Gets or sets the workspace URL.
        /// The fully qualified URL where users can access the Databricks workspace.
        /// </summary>
        [JsonProperty("workspaceUrl", Required = Required.Default)]
        public string WorkspaceUrl { get; set; }

        /// <summary>
        /// Gets or sets the application provisioning state.
        /// Indicates the current state of the workspace resource provisioning process.
        /// </summary>
        [JsonProperty("provisioningState", Required = Required.Default)]
        public ProvisioningState? ProvisioningState { get; set; }

        /// <summary>
        /// Gets or sets the read-only authorizations property that is retrieved from the application package.
        /// Contains the authorization settings and permissions for the workspace.
        /// </summary>
        [JsonProperty("authorizations", Required = Required.Default)]
        public ApplicationAuthorizationEntity[] Authorizations { get; set; }

        /// <summary>
        /// Gets or sets the client entity that created the application.
        /// Contains information about the client (user or service principal) that created the workspace.
        /// </summary>
        [JsonProperty("createdBy", Required = Required.Default)]
        public ApplicationClientDetailsEntity CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the client entity that last updated the application.
        /// Contains information about the client (user or service principal) that last modified the workspace.
        /// </summary>
        [JsonProperty("updatedBy", Required = Required.Default)]
        public ApplicationClientDetailsEntity UpdatedBy { get; set; }

        /// <summary>
        /// Gets or sets the operation status of asynchronous operations.
        /// Provides information about the current status of any long-running operation
        /// being performed on the workspace, including success/failure status and error details.
        /// </summary>
        [JsonProperty("operationStatus", Required = Required.Default)]
        public DBOperationStatusObject OperationStatus { get; set; }

        /// <summary>
        /// Gets or sets the Databricks workspace state.
        /// Indicates whether the Databricks workspace is currently active or inactive
        /// within the Databricks control plane.
        /// </summary>
        [JsonProperty("dbWorkspaceState", Required = Required.Default)]
        public DBWorkspaceState DBWorkspaceState { get; set; }

        /// <summary>
        /// Gets or sets the default catalog configuration.
        /// Contains information about the default catalog type and name
        /// used for data storage in the workspace.
        /// </summary>
        [JsonProperty("defaultCatalog", Required = Required.Default)]
        public DefaultCatalog DefaultCatalog { get; set; }

        /// <summary>
        /// Gets or sets whether Unity Catalog is enabled for this workspace.
        /// Unity Catalog provides a unified governance layer for data assets
        /// across Databricks workspaces.
        /// </summary>
        [JsonProperty("isUcEnabled", Required = Required.Default)]
        public bool IsUcEnabled { get; set; }

        /// <summary>
        /// Gets or sets the compute mode for the workspace.
        /// Indicates whether the workspace uses Serverless or Hybrid compute mode.
        /// </summary>
        [JsonProperty("computeMode", Required = Required.Default)]
        public ComputeMode? ComputeMode { get; set; }
    }
}
