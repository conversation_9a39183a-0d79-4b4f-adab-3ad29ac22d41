//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http.Headers;
    using System.Runtime.CompilerServices;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Http.Extensions;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Net.Http.Headers;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Metadata;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.BaseFeature;



    /// <summary>
    /// The abstract Controller class.
    /// </summary>
    /// <typeparam name="T">The workspace object type</typeparam>
    public abstract class WorkspaceControllerBase<T> : ControllerBase where T : BaseWorkspace
    {
        /// <summary>
        /// Gets the default retry after interval for appliance operations.
        /// </summary>
        private static TimeSpan DefaultRetryAfterInterval
        {
            get
            {
                return CloudConfigurationManager.GetConfigurationTimeSpan(
                    settingName: ProviderConstants.Databricks.RetryIntervalSetting,
                    defaultValue: TimeSpan.FromSeconds(15));
            }
        }

        /// <summary>
        /// Gets or sets the resource type.
        /// </summary>
        protected string ResourceType { get; set; }

        /// <summary>
        /// Gets or sets the provider configuration.
        /// </summary>
        protected IApplicationProviderConfiguration ProviderConfiguration { get; set; }

        /// <summary>
        /// Gets the event service logger.
        /// </summary>
        protected ICommonEventSource Logger
        {
            get { return this.ProviderConfiguration.EventSource; }
        }

        /// <summary>
        /// Gets or sets the resource provider name space.
        /// </summary>
        protected string ResourceProviderNamespace { get; set; }

        /// <summary>
        /// Gets or sets the application API engine.
        /// </summary>
        protected ApplicationApiEngine ApplicationApiEngine { get; set; }

        /// <summary>
        /// Gets or sets the Async Support.
        /// </summary>
        protected virtual bool AsyncSupport
        {
            get
            {
                return false;
            }
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ControllerBase{T}" /> class.
        /// </summary>
        /// <param name="providerConfiguration">The application provider configuration.</param>
        /// <param name="accessConnectorProviderConfiguration">The accessConnector provider configuration.</param>
        public WorkspaceControllerBase(
            IApplicationProviderConfiguration providerConfiguration,
            IAccessConnectorProviderConfiguration accessConnectorProviderConfiguration)
        {
            this.ResourceType = ProviderConstants.Databricks.WorkspacesResourceType;
            this.ProviderConfiguration = providerConfiguration;
            this.ResourceProviderNamespace = ProviderConstants.Databricks.ResourceProviderNamespace;

            this.ApplicationApiEngine = new ApplicationApiEngine(
                providerConfiguration: providerConfiguration,
                accessConnectorProviderConfiguration: accessConnectorProviderConfiguration,
                frontdoorEngine: FrontdoorEngineProviderConfiguration.Instance.FrontdoorEngine);
        }

        /// <summary>
        /// Puts an application.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroup">The resource group name.</param>
        /// <param name="workspaceName">The application name.</param>
        [HttpPut]
        public virtual async Task<IActionResult> PutApplication(
            string subscriptionId,
            string resourceGroup,
            string workspaceName,
            [FromBody] T workspace)
        {
            this.Logger.Debug(
                this.GetMethodName(),
                $"Received a PUT request for subscription: {subscriptionId} resource group: {resourceGroup} workspace name: {workspaceName}");

            var requestProcessingStartDateTime = DateTime.UtcNow;

            this.Logger.Debug(this.GetMethodName(), $"Request content: {workspace.ToJson()}");

            Validation.Subscription(subscriptionId);
            Validation.ResourceGroup(resourceGroup);
            Validation.ResourceName(workspaceName);

            workspace = this.GetWorkspaceFromRequest(subscriptionId, resourceGroup, workspaceName, workspace);

            this.ValidatePut(workspace);

            var incomingEntity = this.GetApplianceEntityFromWorkspace(workspace);

            Request.Headers.TryGetValue(ProviderConstants.SystemDataHeaderName, out var systemDataHeaders);
            incomingEntity.SystemData = SystemDataExtension.GetSystemData(systemDataHeaders.FirstOrDefault());

            // We are receiving Obo Token during write operation to use for deploying prod main template with MI resource.
            // Refer https://armwiki.azurewebsites.net/authorization/getting_obo_token.html?q=obo for full details.
            var signedOboToken = this.Request.Headers.GetFirstOrDefault(ProviderConstants.SignedOboToken);

            var returnValue = await this.ApplicationApiEngine
                .PutApplication(incomingEntity, requestProcessingStartDateTime, signedOboToken, this.AsyncSupport)
                .ConfigureAwait(continueOnCapturedContext: false);

            var savedEntity = returnValue.Item1;
            var jobId = returnValue.Item2;
            var responseStatusCode = returnValue.Item3;

            this.Logger.Debug(this.GetMethodName(), "Converting the updated applianceEntity object to Workspace object.");

            var savedWorkspace = this.GetWorkspaceFromApplianceEntity(savedEntity);

            this.Logger.Debug(this.GetMethodName(), $"Generating and returning the response, job id {jobId}.");

            if (jobId == null)
            {
                return StatusCode((int)responseStatusCode, savedWorkspace);
            }
            else
            {
                return this.CreateAzureAsyncResponse(
                    responseStatusCode,
                    savedWorkspace,
                    jobId);
            }
        }

        /// <summary>
        /// Patches an application.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroup">The resource group name.</param>
        /// <param name="workspaceName">The application name.</param>
        [HttpPatch]
        public virtual async Task<IActionResult> PatchApplication(
            string subscriptionId,
            string resourceGroup,
            string workspaceName,
            [FromBody] T workspace)
        {
            this.Logger.Debug(
                this.GetMethodName(),
                $"Received a PATCH request for subscription: {subscriptionId} resource group: {resourceGroup} workspace name: {workspaceName}");

            this.Logger.Debug(this.GetMethodName(), $"Request content: {workspace.ToJson()}");

            workspace = this.GetWorkspaceFromRequest(subscriptionId, resourceGroup, workspaceName, workspace);

            this.ValidatePatch(workspace);

            this.Logger.Debug(this.GetMethodName(), "Generating an ApplianceEntity object from the incoming request object");

            var incomingEntity = this.GetApplianceEntityFromWorkspace(workspace);

            Request.Headers.TryGetValue(ProviderConstants.SystemDataHeaderName, out var systemDataHeaders);
            incomingEntity.SystemData = SystemDataExtension.GetSystemData(systemDataHeaders.FirstOrDefault());

            this.Logger.Debug(this.GetMethodName(), "Processing the PATCH request");

            var savedEntity = await this.ApplicationApiEngine
                .PatchAppliance(incomingEntity)
                .ConfigureAwait(continueOnCapturedContext: false);

            this.Logger.Debug(this.GetMethodName(), "Converting the updated applianceEntity object to Workspace object.");

            var savedWorkspace = this.GetWorkspaceFromApplianceEntity(savedEntity);

            this.Logger.Debug(this.GetMethodName(), "Generating and returning the response.");

            return Ok(savedWorkspace);
        }

        /// <summary>
        /// Retrieves all applications within the subscription.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        [HttpGet("/subscriptions/{subscriptionId}/providers/Microsoft.Databricks/workspaces")]
        public virtual async Task<IActionResult> GetApplications(string subscriptionId)
        {
            this.Logger.Debug(
                this.GetMethodName(),
                $"Received a GET request for subscription: {subscriptionId}");
            var requestUri = new Uri(this.Request.GetEncodedUrl());
            var applianceEntities = await this.ApplicationApiEngine
                .GetApplications(
                    subscriptionId: subscriptionId,
                    requestUri: new Uri(this.Request.GetEncodedUrl()))
                .ConfigureAwait(continueOnCapturedContext: false);

            this.Logger.Debug(
                this.GetMethodName(),
                $"Converting the list of entities to workspace objects");

            var workspaces = applianceEntities.Entities.SelectArray(
                    (applianceEntity) =>
                    {
                        var workspace = this.GetWorkspaceFromApplianceEntity(applianceEntity);

                        return workspace;
                    });

            return CreateResponse(workspaces, applianceEntities);
        }

        /// <summary>
        /// Retrieves all applications within the specified resource group.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroup">The resource group name.</param>
        public virtual async Task<IActionResult> GetApplications(string subscriptionId, string resourceGroup)
        {
            this.Logger.Debug(
                this.GetMethodName(),
                $"Received a GET request for subscription: {subscriptionId} resource group {resourceGroup}");

            var applianceEntities = await this.ApplicationApiEngine
                .GetApplications(
                    subscriptionId: subscriptionId,
                    resourceGroupName: resourceGroup,
                    requestUri: new Uri(this.Request.GetEncodedUrl()))
                .ConfigureAwait(continueOnCapturedContext: false);

            this.Logger.Debug(
                this.GetMethodName(),
                $"Converting the list of entities to workspace objects");

            var appliances = applianceEntities.Entities.SelectArray(
                (applianceEntity) =>
                {
                    var workspace =  this.GetWorkspaceFromApplianceEntity(applianceEntity);
                    return workspace;
                });

            return CreateResponse(appliances, applianceEntities);
        }

        /// <summary>
        /// Converts list of workspaces into OData compliant resource
        /// </summary>
        /// <param name="appliances">List of Workspaces to return</param>
        /// <param name="applianceEntities">Appliance Entities</param>
        private IActionResult CreateResponse(T[] appliances, SegmentedApplianceEntities applianceEntities)
        {
            this.Logger.Debug(
                this.GetMethodName(),
                $"Generating a response for the GET request. Total number of workspace : {appliances?.Length}");

            return this.StatusCode(
                statusCode: HttpStatusCode.OK,
                value: appliances,
                continuationToken: applianceEntities.ContinuationToken);
        }

        /// <summary>
        /// Gets an application.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroup">The resource group name.</param>
        /// <param name="workspaceName">The application name.</param>
        [HttpGet]
        public virtual async Task<IActionResult> GetApplication(
            string subscriptionId,
            string resourceGroup,
            string workspaceName)
        {
            this.Logger.Debug(
                this.GetMethodName(),
                $"Received a GET request for subscription: {subscriptionId} resource group {resourceGroup} workspace name {workspaceName}");

            var applianceEntity = await this.ApplicationApiEngine
                .GetApplication(
                    subscriptionId: subscriptionId,
                    resourceGroupName: resourceGroup,
                    applicationName: workspaceName)
                .ConfigureAwait(continueOnCapturedContext: false);

            this.Logger.Debug(
                this.GetMethodName(),
                $"Converting the list of entities to workspace object");

            var appliance = this.GetWorkspaceFromApplianceEntity(applianceEntity);

            this.Logger.Debug(
                this.GetMethodName(),
                $"Generating a response for the GET request - {appliance.ToJson()}");

            return Ok(appliance);
        }

        /// <summary>
        /// Deletes an application.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroup">The resource group name.</param>
        /// <param name="workspaceName">The application name.</param>
        /// <param name="keepUcData">Customer provided input for retainUcData.</param>
        [HttpDelete]
        public virtual async Task<IActionResult> DeleteApplication(
            string subscriptionId,
            string resourceGroup,
            string workspaceName,
            bool keepUcData = true)
        {
            this.Logger.Debug(
                this.GetMethodName(),
                $"Received a DELETE request for subscription: {subscriptionId} resource group {resourceGroup}" +
                $" workspace name {workspaceName}, retainUcData: {keepUcData}");

            var requestProcessingStartDateTime = DateTime.UtcNow;

            var applianceEntity = await this.ApplicationApiEngine
                .DeleteApplication(
                    subscriptionId: subscriptionId,
                    resourceGroupName: resourceGroup,
                    workspaceName: workspaceName,
                    retainUcData: keepUcData,
                    requestProcessingStartDateTime: requestProcessingStartDateTime)
                .ConfigureAwait(continueOnCapturedContext: false);

            if (applianceEntity == null)
            {
                this.Logger.Debug(
                this.GetMethodName(),
                $"Workspace name {workspaceName} for {subscriptionId} resource group {resourceGroup} does not exist. Returning Status NoContent");

                return NoContent();
            }
            else
            {
                var jobId = ApplianceDeprovisioningJobMetadata.GetJobId(resourceGroup, workspaceName);

                this.Logger.Debug(
                this.GetMethodName(),
                $"Workspace name {workspaceName} for {subscriptionId} resource group {resourceGroup} exists. Queueing deletion job {jobId}");

                return this.CreateAsyncResponse(applianceEntity, jobId);
            }
        }

        /// <summary>
        /// Reads the request object content to return a workspace API object which is derived from Appliance class.
        /// </summary>
        /// <param name="subscriptionId">The subscription id</param>
        /// <param name="resourceGroupName">The resource group name</param>
        /// <param name="workspaceName">The workspace name</param>
        /// <returns>The workspace object</returns>
        protected virtual T GetWorkspaceFromRequest(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            [FromBody] T workspace)
        {
            if (workspace == null)
            {
                throw new ErrorResponseMessageException(
                    httpStatus: HttpStatusCode.BadRequest,
                    errorCode: ErrorResponseCode.InvalidApplicationDefinition,
                    errorMessage: ErrorResponseMessages.ApplicationDefinitionNull.ToLocalizedMessage(workspaceName));
            }

            workspace.FullyQualifiedName = workspaceName;
            workspace.ResourceGroup = resourceGroupName;
            workspace.SubscriptionId = subscriptionId;

            return workspace;
        }

        /// <summary>
        /// Gets appliance entity from workspace object
        /// </summary>
        /// <param name="workspace">The workspace object</param>
        /// <returns>The appliance entity object</returns>
        protected abstract ApplianceEntity GetApplianceEntityFromWorkspace(T workspace);

        /// <summary>
        /// Get a workspace object from appliance entity object
        /// </summary>
        /// <param name="applianceEntity">The appliance entity object</param>
        /// <returns>The workspace object</returns>
        protected abstract T GetWorkspaceFromApplianceEntity(ApplianceEntity applianceEntity);

        /// <summary>
        /// Validates the workspace object for PUT requests
        /// </summary>
        /// <param name="workspace">The workspace object</param>
        protected abstract void ValidatePut(T workspace);

        /// <summary>
        /// Validates workspace for patch requests
        /// </summary>
        /// <param name="workspace">The workspace object</param>
        protected abstract void ValidatePatch(T workspace);

        /// <summary>
        /// A helper method to retrieve the current class and method name,
        /// </summary>
        /// <param name="callerName">The name of the caller</param>
        /// <returns>The method name</returns>
        protected string GetMethodName([CallerMemberName] string callerName = null)
        {
            return $"{this.GetType().Name}-{callerName}";
        }

        /// <summary>
        /// Creates an asynchronous response object.
        /// </summary>
        /// <param name="statusCode">The status code</param>
        /// <param name="workspace">The workspace object</param>
        /// <param name="jobId">The job id</param>
        /// <returns>The response object</returns>
        public IActionResult CreateAzureAsyncResponse(
            HttpStatusCode statusCode,
            T workspace,
            string jobId)
        {
            var result = StatusCode(statusCode: (int) statusCode, value: workspace);

            Response.Headers.Add(HeaderNames.RetryAfter, new RetryConditionHeaderValue(DefaultRetryAfterInterval).ToString());
            var asyncOperationUri = this.GetAsyncOperationUri(
                subscriptionId: workspace.SubscriptionId,
                resourceGroupName: workspace.ResourceGroup,
                location: workspace.Location,
                fullyQualifiedResourceId: workspace.Id,
                jobId: jobId).ToString();

            Response.Headers.Add(
               RequestCorrelationContext.HeaderAzureAsyncOperation,
               asyncOperationUri);

            return result;
        }
    }
}
